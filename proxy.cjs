const puppeteer = require("puppeteer-extra");
const StealthPlugin = require("puppeteer-extra-plugin-stealth");

// Enhanced stealth configuration for better Cloudflare bypass
const stealth = StealthPlugin();
// Remove problematic evasions that can be detected
stealth.enabledEvasions.delete("navigator.plugins");
stealth.enabledEvasions.delete("navigator.languages");
stealth.enabledEvasions.delete("navigator.webdriver");
// Keep essential evasions
stealth.enabledEvasions.add("navigator.permissions");
stealth.enabledEvasions.add("chrome.runtime");
stealth.enabledEvasions.add("iframe.contentWindow");
stealth.enabledEvasions.add("media.codecs");

puppeteer.use(stealth);

const { simpleParser } = require("mailparser");
const { promisify } = require("util");
const mysql = require("mysql2/promise");
const { faker } = require("@faker-js/faker");
const UserAgent = require("user-agents");
const { parse } = require("useragent");

const pool = mysql.createPool({
  host: "************",
  user: "otp",
  password: "46EGpepWDwxGeNfK",
  database: "otp",
});

// Enhanced anti-detection utilities
function getRandomUserAgent() {
  const userAgents = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
  ];
  return userAgents[Math.floor(Math.random() * userAgents.length)];
}

function getRandomViewport() {
  const viewports = [
    { width: 1920, height: 1080 },
    { width: 1366, height: 768 },
    { width: 1536, height: 864 },
    { width: 1440, height: 900 },
    { width: 1280, height: 720 },
  ];
  return viewports[Math.floor(Math.random() * viewports.length)];
}

// Human-like delay function
function randomDelay(min = 1000, max = 3000) {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

(async () => {
  // Proxy configuration
  const proxyServer = "gw.dataimpulse.com:823";
  const proxyUsername = "888b677829c7ffc4684b";
  const proxyPassword = "3a07caeb6bae627b";

  while (true) {
    // Get random viewport for this session
    const viewport = getRandomViewport();

    const browser = await puppeteer.launch({
      executablePath:
        "C:/Program Files/BraveSoftware/Brave-Browser/Application/brave.exe",
      headless: false,
      defaultViewport: viewport,
      args: [
        "--incognito",
        "--no-sandbox",
        "--disable-setuid-sandbox",
        "--disable-infobars",
        "--disable-notifications",
        "--disable-popup-blocking",
        "--disable-save-password-bubble",
        "--disable-translate",
        "--disable-features=VizDisplayCompositor",
        "--disable-ipc-flooding-protection",
        "--disable-renderer-backgrounding",
        "--disable-backgrounding-occluded-windows",
        "--disable-client-side-phishing-detection",
        "--disable-component-extensions-with-background-pages",
        "--disable-default-apps",
        "--disable-dev-shm-usage",
        "--disable-extensions",
        "--disable-features=TranslateUI",
        "--disable-hang-monitor",
        "--disable-prompt-on-repost",
        "--disable-sync",
        "--disable-web-security",
        "--enable-automation=false",
        "--exclude-switches=enable-automation",
        "--force-color-profile=srgb",
        "--metrics-recording-only",
        "--no-crash-upload",
        "--no-default-browser-check",
        "--no-first-run",
        "--password-store=basic",
        "--use-mock-keychain",
        "--hide-scrollbars",
        "--mute-audio",
        "--no-zygote",
        "--disable-gpu",
        "--disable-dev-shm-usage",
        "--disable-accelerated-2d-canvas",
        "--no-sandbox",
        "--disable-setuid-sandbox",
        "--disable-dev-shm-usage",
        "--disable-accelerated-2d-canvas",
        "--disable-accelerated-jpeg-decoding",
        "--disable-accelerated-mjpeg-decode",
        "--disable-accelerated-video-decode",
        "--disable-app-list-dismiss-on-blur",
        "--disable-background-timer-throttling",
        "--disable-backgrounding-occluded-windows",
        "--disable-renderer-backgrounding",
        "--disable-features=TranslateUI,BlinkGenPropertyTrees",
        "--disable-ipc-flooding-protection",
        `--window-size=${viewport.width},${viewport.height}`,
        "--window-position=0,0",
        "--ignore-certificate-errors",
        "--ignore-ssl-errors",
        "--ignore-certificate-errors-spki-list",
        "--allow-running-insecure-content",
        "--disable-features=IsolateOrigins,site-per-process",
        `--proxy-server=${proxyServer}`,
      ],
    });

    const [page] = await browser.pages();

    // Authenticate with the proxy
    await page.authenticate({
      username: proxyUsername,
      password: proxyPassword,
    });

    // Enhanced page setup for better stealth
    await page.setBypassCSP(true);
    await page.setRequestInterception(true);

    // Set random user agent for this session
    const userAgent = getRandomUserAgent();
    await page.setUserAgent(userAgent);
    console.log(`🎭 Using User Agent: ${userAgent}`);

    // Enhanced headers to mimic real browser
    await page.setExtraHTTPHeaders({
      Accept:
        "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
      "Accept-Language": "en-US,en;q=0.9",
      "Accept-Encoding": "gzip, deflate, br",
      "Cache-Control": "no-cache",
      Pragma: "no-cache",
      "Sec-Ch-Ua":
        '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
      "Sec-Ch-Ua-Mobile": "?0",
      "Sec-Ch-Ua-Platform": '"Windows"',
      "Sec-Fetch-Dest": "document",
      "Sec-Fetch-Mode": "navigate",
      "Sec-Fetch-Site": "none",
      "Sec-Fetch-User": "?1",
      "Upgrade-Insecure-Requests": "1",
      Referer: "https://in.bookmyshow.com/",
    });

    page.on("console", (message) => {
      const type = message.type().substr(0, 3).toUpperCase();
      const colors = {
        LOG: "\x1b[37m",
        ERR: "\x1b[31m",
        WAR: "\x1b[33m",
        INF: "\x1b[34m",
      };
      const color = colors[type] || colors.LOG;
      console.log(`${color}${type} ${message.text()}\x1b[0m`);

      // If you want to capture stack traces for errors
      if (message.type() === "error") {
        //  console.log(message.location());
        //  console.log(message.stackTrace());
      }
    });

    // Enhanced request interception logic
    page.on("request", (request) => {
      try {
        const url = request.url();

        // Block known bot detection and tracking requests
        const blockedDomains = [
          "gum.criteo.com",
          "googletagmanager.com",
          "google-analytics.com",
          "doubleclick.net",
          "facebook.com/tr",
          "connect.facebook.net",
          "hotjar.com",
          "fullstory.com",
          "logrocket.com",
          "datadoghq.com",
        ];

        if (blockedDomains.some((domain) => url.includes(domain))) {
          console.log(`🚫 Blocked tracking request: ${url}`);
          request.abort();
          return;
        }

        // Handle preflight OPTIONS requests
        if (request.method() === "OPTIONS") {
          request.continue();
          return;
        }

        // Enhanced headers for all requests
        const headers = Object.assign({}, request.headers(), {
          Origin: "https://in.bookmyshow.com",
          Referer: "https://in.bookmyshow.com/",
          "Sec-Fetch-Site": url.includes("bookmyshow.com")
            ? "same-origin"
            : "cross-site",
          "Sec-Fetch-Mode": "cors",
          "Sec-Fetch-Dest":
            request.resourceType() === "document" ? "document" : "empty",
        });

        // Remove automation-related headers
        delete headers["sec-ch-ua-automation"];
        delete headers["webdriver"];

        request.continue({ headers });
      } catch (error) {
        console.error(`Request handling error: ${error.message}`);
        try {
          request.continue();
        } catch (e) {
          // Request already handled
        }
      }
    });

    // Enhanced response handling with Cloudflare detection and retry logic
    page.on("response", async (response) => {
      const status = response.status();
      const url = response.url();

      if (status === 401) {
        console.log("🔒 Authentication required for:", url);
      } else if (status === 403) {
        console.log("🚫 Access forbidden (possible Cloudflare block):", url);
        // Check if it's a Cloudflare challenge
        if (
          response.headers()["server"]?.includes("cloudflare") ||
          response.headers()["cf-ray"]
        ) {
          console.log("☁️  Cloudflare challenge detected!");

          // If it's the OTP endpoint being blocked, try to handle it
          if (url.includes("/api/members/otp/send")) {
            console.log(
              "🔄 OTP endpoint blocked, implementing bypass strategy..."
            );
            // Add longer delay to let Cloudflare settle
            setTimeout(async () => {
              try {
                // Try to trigger the request again by interacting with the page
                await page.evaluate(() => {
                  // Try to find and click submit button again
                  const submitButtons = document.querySelectorAll(
                    'button[type="submit"], button:contains("Send"), button:contains("Continue")'
                  );
                  if (submitButtons.length > 0) {
                    submitButtons[0].click();
                  }
                });
              } catch (e) {
                console.log("⚠️  Retry attempt failed:", e.message);
              }
            }, 5000);
          }
        }
      } else if (status === 429) {
        console.log("⏰ Rate limited:", url);
        // Add delay for rate limiting
        await sleep(randomDelay(3000, 8000));
      } else if (status >= 500) {
        console.log("🔥 Server error:", status, url);
      }

      // Log successful responses to important endpoints
      if (status === 200 && url.includes("bookmyshow.com")) {
        console.log("✅ Successful request to:", url);
      }
    });

    // Enhanced browser fingerprint spoofing
    await page.evaluateOnNewDocument(() => {
      // Override navigator properties to appear more human
      Object.defineProperty(navigator, "hardwareConcurrency", {
        get: () => 4,
      });
      Object.defineProperty(navigator, "deviceMemory", {
        get: () => 8,
      });
      Object.defineProperty(navigator, "platform", {
        get: () => "Win32",
      });
      Object.defineProperty(navigator, "maxTouchPoints", {
        get: () => 0,
      });

      // Remove webdriver property
      delete navigator.__proto__.webdriver;

      // Override permissions API
      const originalQuery = window.navigator.permissions.query;
      window.navigator.permissions.query = (parameters) =>
        parameters.name === "notifications"
          ? Promise.resolve({ state: Notification.permission })
          : originalQuery(parameters);

      // Override plugins
      Object.defineProperty(navigator, "plugins", {
        get: () => [1, 2, 3, 4, 5],
      });

      // Override languages
      Object.defineProperty(navigator, "languages", {
        get: () => ["en-US", "en"],
      });

      // Override chrome runtime
      window.chrome = {
        runtime: {},
      };

      // Override screen properties
      Object.defineProperty(screen, "availHeight", {
        get: () => 1040,
      });
      Object.defineProperty(screen, "availWidth", {
        get: () => 1920,
      });
    });

    // Check and print the actual IP address being used through proxy
    try {
      console.log("🌐 Checking external IP address through proxy...");
      await page.goto("https://httpbin.org/ip", {
        waitUntil: "networkidle2",
        timeout: 30000,
      });

      const ipContent = await page.content();
      const ipMatch = ipContent.match(/"origin": "([^"]+)"/);
      if (ipMatch) {
        console.log(`✅ External IP through proxy: ${ipMatch[1]}`);
      } else {
        console.log("❌ Could not determine external IP");
      }
    } catch (error) {
      console.log(`⚠️  IP check failed: ${error.message}`);
    }

    // Pre-visit BookMyShow to establish session and get past initial Cloudflare checks
    console.log("🔄 Pre-establishing session with BookMyShow...");
    try {
      await page.goto("https://in.bookmyshow.com/", {
        waitUntil: "networkidle2",
        timeout: 30000,
      });

      // Wait for any Cloudflare challenges to complete
      await sleep(randomDelay(3000, 6000));

      // Check if we're on a challenge page and wait
      const title = await page.title();
      if (
        title.includes("Just a moment") ||
        title.includes("Checking your browser")
      ) {
        console.log("☁️  Initial Cloudflare challenge detected, waiting...");
        await sleep(15000);
      }

      console.log("✅ Session established with BookMyShow");
    } catch (error) {
      console.log("⚠️  Session establishment failed:", error.message);
    }

    const movie_url =
      "https://in.bookmyshow.com/movies/mumbai/oneness-amata-oina/**********"; //await getmovieurl();

    // Step 1: Navigate to the URL with enhanced options
    console.log("🎬 Navigating to movie page...");
    try {
      await page.goto(movie_url, {
        waitUntil: "networkidle2",
        timeout: 60000, // Increased timeout for Cloudflare challenges
      });

      // Wait for potential Cloudflare challenge to complete
      await sleep(randomDelay(2000, 5000));

      // Check if we're on a Cloudflare challenge page
      const title = await page.title();
      if (
        title.includes("Just a moment") ||
        title.includes("Checking your browser")
      ) {
        console.log("☁️  Cloudflare challenge detected, waiting...");
        await sleep(10000); // Wait for challenge to complete
      }
    } catch (error) {
      console.log("⚠️  Navigation error:", error.message);
      // Try to continue anyway
    }

    // Step 2: Wait for the page to load and find "I'm interested" button
    console.log("🔍 Looking for 'I'm interested' button...");
    await sleep(randomDelay(2000, 4000));

    try {
      await page.evaluate(() => {
        const buttons = [...document.querySelectorAll("button")];
        const targetButton = buttons.find((btn) =>
          btn.innerText.includes("I'm interested")
        );
        if (targetButton) {
          // Scroll into view first
          targetButton.scrollIntoView({ behavior: "smooth", block: "center" });
          setTimeout(() => targetButton.click(), 500);
          return true;
        }
        return false;
      });
      console.log("✅ 'I'm interested' button clicked");
    } catch (error) {
      console.log("⚠️  Could not find 'I'm interested' button:", error.message);
    }

    // Step 3: Wait for the popup to appear with better error handling
    console.log("🔍 Waiting for email login popup...");
    try {
      await page.waitForSelector('img[alt="email logo"]', {
        timeout: 15000,
        visible: true,
      });
      console.log("✅ Email logo found, proceeding...");
    } catch (error) {
      console.log("❌ Email logo not found, checking page content...");
      const pageContent = await page.content();
      if (
        pageContent.includes("cloudflare") ||
        pageContent.includes("challenge")
      ) {
        console.log("☁️  Cloudflare challenge detected in page content");
      }
      // Don't exit immediately, try to continue
    }

    await sleep(randomDelay(1500, 3000));
    // Step 4: Click on "Continue with Email" with enhanced error handling
    console.log("📧 Attempting to click email logo...");
    async function clickEmailLogoAndRestart() {
      try {
        // Wait for element to be clickable
        await page.waitForSelector('img[alt="email logo"]', {
          timeout: 10000,
          visible: true,
        });

        // Human-like click with slight delay
        await sleep(randomDelay(500, 1500));
        await page.click('img[alt="email logo"]');
        console.log("✅ Email logo clicked successfully");
        return true;
      } catch (error) {
        console.log("❌ Email logo click failed:", error.message);
        return false;
      }
    }

    const emailClicked = await clickEmailLogoAndRestart();
    if (!emailClicked) {
      console.log("⚠️  Continuing without email click...");
    }

    // Step 5: Enter random email address with human-like typing
    console.log("⌨️  Generating and entering email...");
    const randomEmail = await generateRandomEmail();

    try {
      // Wait for email input field
      await page.waitForSelector(
        'input[type="email"], input[name="email"], input[placeholder*="email"]',
        {
          timeout: 10000,
          visible: true,
        }
      );

      // Clear any existing content and type email with human-like delays
      await page.focus(
        'input[type="email"], input[name="email"], input[placeholder*="email"]'
      );
      await page.keyboard.down("Control");
      await page.keyboard.press("KeyA");
      await page.keyboard.up("Control");
      await page.keyboard.press("Backspace");

      // Type email with random delays between characters
      for (const char of randomEmail) {
        await page.keyboard.type(char);
        await sleep(randomDelay(50, 150));
      }

      console.log("✅ Email entered:", randomEmail);
    } catch (error) {
      console.log(
        "⚠️  Email input error, using fallback method:",
        error.message
      );
      await page.keyboard.type(randomEmail);
    }

    // Step 6: Submit email form
    console.log("📤 Submitting email form...");
    await sleep(randomDelay(1000, 2000));
    await page.keyboard.press("Enter");

    // Step 7: Wait and check for OTP
    console.log("⏳ Waiting for OTP...");
    const otp = await checkForOtp(randomEmail);

    // Step 8: Enter OTP with human-like typing
    console.log("🔢 Entering OTP:", otp);
    try {
      // Wait for OTP input field
      await page.waitForSelector(
        'input[type="text"], input[name="otp"], input[placeholder*="OTP"]',
        {
          timeout: 15000,
          visible: true,
        }
      );

      // Type OTP with delays
      for (const digit of otp) {
        await page.keyboard.type(digit);
        await sleep(randomDelay(100, 300));
      }

      console.log("✅ OTP entered successfully");
    } catch (error) {
      console.log("⚠️  OTP input error, using fallback:", error.message);
      await page.keyboard.type(otp, { delay: 200 });
    }

    await sleep(randomDelay(2000, 4000));
    // Step 10: Wait for the popup to close (or manually trigger it)
    await page.evaluate(() => {
      const buttons = [...document.querySelectorAll("button")];
      const targetButton = buttons.find((btn) =>
        btn.innerText.includes("I'm interested")
      );
      if (targetButton) {
        targetButton.click();
      }
    });
    await sleep(2000);
    // Cleanup
    await browser.close();
    await sleep(2000);
  }
})();

// Function to pick a random domain and generate a random email ID
async function generateRandomEmail() {
  // Pick a random domain
  const randomDomain = await getdomains();
  const firstName = faker.person.firstName();
  const lastName = faker.person.lastName();
  return `${firstName.toLowerCase()}.${lastName.toLowerCase()}@${lastName.toLowerCase()}.${randomDomain}`;
  //   const url = new URL("https://app.addy.io/api/v1/aliases");

  //   const headers = {
  //     Authorization:
  //       "Bearer addy_io_dCU7mWPzImXxn6F1TZpbuYMe6tDn2c9I50OjxHpm34da2671", // Add your Bearer token here
  //     "Content-Type": "application/json",
  //     "X-Requested-With": "XMLHttpRequest",
  //     Accept: "application/json",
  //   };

  //   let body = {
  //     domain: randomDomain,
  //     description: "For example.com",
  //     format: "random_words",
  //   };

  //   try {
  //     const response = await fetch(url, {
  //       method: "POST",
  //       headers,
  //       body: JSON.stringify(body),
  //     });

  //     if (!response.ok) {
  //       throw new Error(`HTTP error! Status: ${response.status}`);
  //     }

  //     const data = await response.json();
  //     console.log("Retrieved data:", data.data.email);

  //     // Return the generated email
  //     return data.data.email;
  //   } catch (error) {
  //     console.error("Error:", error);
  //     return null; // Return null in case of an error
  //   }
}

async function sleep(ms) {
  return new Promise((resolve) => {
    setTimeout(resolve, ms);
  });
}
// Function to check email and capture OTP
async function checkForOtp(email) {
  const pollInterval = 10000; // 10 seconds
  const timeout = 120000; // 2 minutes (120 seconds)
  const startTime = Date.now();
  let otp;

  while (!otp) {
    try {
      const elapsedTime = Date.now() - startTime;
      if (elapsedTime >= timeout) {
        console.log("Timeout exceeded, no OTP found");
        return "000000"; // Return null or a custom message if timeout is reached
      }

      const connection = await pool.getConnection();
      const [rows] = await connection.execute(
        "SELECT otp FROM emails WHERE to_mail = ?",
        [email]
      );
      connection.release();

      if (rows.length > 0) {
        otp = rows[0].otp;
        console.log("OTP retrieved:", otp);
        return otp; // Return the OTP once found
      } else {
        console.log("OTP not found yet, retrying...");
      }

      // Wait for the specified interval before checking again
      await new Promise((resolve) => setTimeout(resolve, pollInterval));
    } catch (error) {
      console.error("Error checking for OTP:", error);
      // You can choose to break the loop on certain errors or continue
    }
  }
}

async function getmovieurl() {
  const connection = await pool.getConnection();
  const [rows] = await connection.execute(
    "SELECT url FROM movie ORDER BY RAND( ) limit 1"
  );
  connection.release();
  return rows[0].url;
}

async function getdomains() {
  const connection = await pool.getConnection();
  const [rows] = await connection.execute(
    "SELECT name FROM domains WHERE status = 1 ORDER BY RAND( ) LIMIT 1"
  );
  connection.release();
  return rows[0].name;
}
